<?php $__env->startSection('title', 'Quản lý danh mục'); ?>
<?php $__env->startSection('page-title', 'Quản lý danh mục'); ?>
<?php $__env->startSection('meta_description', 'Trang quản lý danh mục sản phẩm, thêm, sửa, x<PERSON><PERSON> danh mục cho hệ thống bán hàng thời trang.'); ?>
<?php $__env->startSection('meta_keywords', 'danh mục, quản lý danh mục, admin, sản phẩm, thời trang'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Card chính -->
    <div class="card card-primary card-outline">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">
                    <i class="fas fa-th mr-2"></i><PERSON>h s<PERSON>ch danh mục
                </h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('category.create')); ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-plus mr-1"></i>Thêm danh mục
                    </a>
                </div>
            </div>
        </div>

        <!-- Thanh tìm kiếm -->
        <div class="card-body pb-0">
            <form action="<?php echo e(route('category.index')); ?>" method="GET" class="mb-3">
                <div class="input-group">
                    <input type="text" name="search" class="form-control"
                           placeholder="Tìm kiếm danh mục..."
                           value="<?php echo e(request('search')); ?>">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if(request('search')): ?>
                        <a href="<?php echo e(route('category.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 80px;" class="text-center">STT</th>
                        <th>Tên danh mục</th>
                        <th style="width: 150px;" class="text-center">Ngày tạo</th>
                        <th style="width: 150px;" class="text-center">Ngày cập nhật</th>
                        <th style="width: 120px;" class="text-center">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="text-center">
                                <span class="badge badge-primary">
                                    <?php echo e(($categories->currentPage() - 1) * $categories->perPage() + $key + 1); ?>

                                </span>
                            </td>
                            <td class="font-weight-medium"><?php echo e($category->name_category); ?></td>
                            <td class="text-center text-muted">
                                <?php echo e($category->created_at ? $category->created_at->format('d/m/Y') : '-'); ?>

                            </td>
                            <td class="text-center text-muted">
                                <?php echo e($category->updated_at ? $category->updated_at->format('d/m/Y') : '-'); ?>

                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="<?php echo e(route('category.edit', $category->id_category)); ?>"
                                       class="btn btn-sm btn-warning"
                                       title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('category.destroy', $category->id_category)); ?>"
                                          method="POST"
                                          style="display:inline-block">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit"
                                                class="btn btn-sm btn-danger btn-delete"
                                                title="Xóa">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p class="mb-0">Không có dữ liệu danh mục</p>
                                    <?php if(request('search')): ?>
                                        <small>Không tìm thấy kết quả cho "<?php echo e(request('search')); ?>"</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if($categories->hasPages()): ?>
        <div class="card-footer clearfix">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Hiển thị <?php echo e($categories->firstItem() ?? 0); ?> đến <?php echo e($categories->lastItem() ?? 0); ?>

                    trong tổng số <?php echo e($categories->total()); ?> kết quả
                </div>
                <div>
                    <?php echo e($categories->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Hiệu ứng chuyển trang mượt mà
        $(document).on('click', '.pagination a', function(event) {
            event.preventDefault();
            var url = $(this).attr('href');
            showLoading();
            setTimeout(function() {
                window.location.href = url;
            }, 300);
        });

        // Tự động focus vào ô tìm kiếm khi trang load
        $('input[name="search"]').focus();

        // Xử lý form tìm kiếm
        $('form').on('submit', function() {
            const searchValue = $('input[name="search"]').val().trim();
            if (searchValue === '') {
                window.location.href = '<?php echo e(route("category.index")); ?>';
                return false;
            }
            showLoading();
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/category/categoryIndex.blade.php ENDPATH**/ ?>