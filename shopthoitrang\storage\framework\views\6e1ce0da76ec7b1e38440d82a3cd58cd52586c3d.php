<?php $__env->startSection('title', 'Quản lý danh mục'); ?>
<?php $__env->startSection('page-title', 'Quản lý danh mục'); ?>
<?php $__env->startSection('meta_description', 'Trang quản lý danh mục sản phẩm, thêm, sửa, x<PERSON><PERSON> danh mục cho hệ thống bán hàng thời trang.'); ?>
<?php $__env->startSection('meta_keywords', 'danh mục, quản lý danh mục, admin, sản phẩm, thời trang'); ?>
<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row mb-2">
        <div class="col-12">
            <a href="<?php echo e(route('category.create')); ?>" class="btn btn-primary mb-3">
                <i class="fas fa-plus"></i> Thêm danh mục
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h1 class="card-title h5">Danh sách danh mục</h1>
            <div class="card-tools">
                <form action="<?php echo e(route('category.index')); ?>" method="GET" class="input-group input-group-sm" style="width: 250px;">
                    <input type="text" name="search" class="form-control float-right" placeholder="Tìm kiếm..." value="<?php echo e(request('search')); ?>" aria-label="Tìm kiếm danh mục">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-default" aria-label="Tìm kiếm"><i class="fas fa-search"></i></button>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body p-0">
            <table class="table table-striped table-hover" aria-describedby="Danh sách danh mục">
                <thead>
                    <tr>
                        <th style="width: 10px">#</th>
                        <th>Tên danh mục</th>
                        <th>Ngày tạo</th>
                        <th>Cập nhật</th>
                        <th style="width: 120px">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e(($categories->currentPage() - 1) * $categories->perPage() + $key + 1); ?></td>
                            <td><?php echo e($category->name_category); ?></td>
                            <td><?php echo e($category->created_at ? $category->created_at->format('d/m/Y') : '-'); ?></td>
                            <td><?php echo e($category->updated_at ? $category->updated_at->format('d/m/Y') : '-'); ?></td>
                            <td>
                                <a href="<?php echo e(route('category.edit', $category->id_category)); ?>" class="btn btn-sm btn-warning" title="Sửa"><i class="fas fa-edit"></i></a>
                                <form action="<?php echo e(route('category.destroy', $category->id_category)); ?>" method="POST" style="display:inline-block">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc muốn xóa?')" title="Xóa"><i class="fas fa-trash"></i></button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center">Không có danh mục nào</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        <div class="card-footer clearfix">
            <?php echo e($categories->links('pagination::bootstrap-4')); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('scripts'); ?>
<script>
    // Hiệu ứng chuyển trang mượt mà
    $(document).on('click', '.pagination a', function(event) {
        event.preventDefault();
        var url = $(this).attr('href');
        $('body').fadeOut(150, function() {
            window.location.href = url;
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/category/categoryIndex.blade.php ENDPATH**/ ?>