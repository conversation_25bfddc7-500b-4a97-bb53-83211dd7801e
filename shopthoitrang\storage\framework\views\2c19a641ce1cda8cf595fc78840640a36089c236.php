
<?php $__env->startSection('title', 'Quản lý bài viết'); ?>
<?php $__env->startSection('page-title', 'Quản lý bài viết'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Card chính -->
    <div class="card card-primary card-outline">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">
                    <i class="fas fa-newspaper mr-2"></i>Danh sách bài viết
                </h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('post.indexaddpost')); ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-plus mr-1"></i>Thêm bài viết
                    </a>
                </div>
            </div>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 80px;" class="text-center">Mã bài viết</th>
                        <th>Tiêu đề bài viết</th>
                        <th>Nội dung</th>
                        <th style="width: 200px;" class="text-center">Hình ảnh</th>
                        <th style="width: 150px;" class="text-center">Ngày tạo</th>
                        <th style="width: 140px;" class="text-center">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td class="text-center">
                            <span class="badge badge-primary"><?php echo e($post->id_post); ?></span>
                        </td>
                        <td class="font-weight-medium">
                            <div>
                                <h6 class="mb-1"><?php echo e(Str::limit($post->title_post, 50)); ?></h6>
                                <?php if(strlen($post->title_post) > 50): ?>
                                    <small class="text-muted" title="<?php echo e($post->title_post); ?>">
                                        <i class="fas fa-info-circle"></i> Hover để xem đầy đủ
                                    </small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="post-content">
                                <?php echo e(Str::limit(strip_tags($post->content_post), 100)); ?>

                                <?php if(strlen(strip_tags($post->content_post)) > 100): ?>
                                    <button class="btn btn-link btn-sm p-0 ml-1"
                                            onclick="showFullContent('<?php echo e(addslashes($post->content_post)); ?>', '<?php echo e(addslashes($post->title_post)); ?>')">
                                        <i class="fas fa-expand-alt"></i> Xem thêm
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class="post-images">
                                <?php if(isset($postImages[$post->id_post]) && count($postImages[$post->id_post]) > 0): ?>
                                    <div class="image-gallery">
                                        <?php $__currentLoopData = $postImages[$post->id_post]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $imageName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($index < 2): ?>
                                                <img src="<?php echo e(asset('uploads/post/' . $imageName)); ?>"
                                                     alt="Post image"
                                                     class="img-thumbnail post-img mr-1 mb-1"
                                                     style="width: 60px; height: 60px; object-fit: cover;">
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(count($postImages[$post->id_post]) > 2): ?>
                                            <span class="badge badge-info">
                                                +<?php echo e(count($postImages[$post->id_post]) - 2); ?> ảnh
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">
                                        <i class="fas fa-image"></i> Không có ảnh
                                    </span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-center text-muted">
                            <?php if($post->created_at): ?>
                                <div><?php echo e($post->created_at->format('d/m/Y')); ?></div>
                                <small><?php echo e($post->created_at->format('H:i')); ?></small>
                            <?php else: ?>
                                <span class="text-muted">Không có</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="<?php echo e(route('post.indexupdatepost', ['id' => $post->id_post])); ?>"
                                   class="btn btn-sm btn-warning"
                                   title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button"
                                        class="btn btn-sm btn-danger btn-delete-post"
                                        data-post-id="<?php echo e($post->id_post); ?>"
                                        data-post-title="<?php echo e($post->title_post); ?>"
                                        title="Xóa bài viết">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-newspaper fa-3x mb-3"></i>
                                <p class="mb-0">Không có bài viết nào</p>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if($posts->hasPages()): ?>
        <div class="card-footer clearfix">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Hiển thị <?php echo e($posts->firstItem() ?? 0); ?> đến <?php echo e($posts->lastItem() ?? 0); ?>

                    trong tổng số <?php echo e($posts->total()); ?> bài viết
                </div>
                <div>
                    <?php echo e($posts->links()); ?>

                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal xem nội dung đầy đủ -->
<div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="contentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contentModalLabel">Nội dung bài viết</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="contentModalBody">
                <!-- Nội dung sẽ được load bằng JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .post-img {
        transition: transform 0.3s ease;
        cursor: pointer;
    }

    .post-img:hover {
        transform: scale(1.5);
        z-index: 1000;
        position: relative;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .post-content {
        max-width: 300px;
        word-wrap: break-word;
    }

    .image-gallery {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
    }

    .table td {
        vertical-align: middle;
    }

    .btn-link {
        color: #007bff;
        text-decoration: none;
    }

    .btn-link:hover {
        color: #0056b3;
        text-decoration: underline;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Xử lý nút xóa bài viết
        $('.btn-delete-post').on('click', function() {
            const postId = $(this).data('post-id');
            const postTitle = $(this).data('post-title');

            Swal.fire({
                title: 'Xác nhận xóa bài viết',
                text: `Bạn có chắc chắn muốn xóa bài viết "${postTitle}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Có, xóa!',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    showLoading();
                    window.location.href = `<?php echo e(route('post.deletepost', '')); ?>/${postId}`;
                }
            });
        });

        // Hiệu ứng chuyển trang mượt mà
        $(document).on('click', '.pagination a', function(event) {
            event.preventDefault();
            var url = $(this).attr('href');
            showLoading();
            setTimeout(function() {
                window.location.href = url;
            }, 300);
        });
    });

    // Hàm hiển thị nội dung đầy đủ
    function showFullContent(content, title) {
        $('#contentModalLabel').text(title);
        $('#contentModalBody').html(content);
        $('#contentModal').modal('show');
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/post/listpost.blade.php ENDPATH**/ ?>