<?php 
namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Manufacturer;
use App\Models\Product;
use App\Models\Category;
use App\Models\Cart;
use App\Models\Order;
use Illuminate\Support\Facades\Session;

class ManufacturerControllerUser extends Controller
{
    public function indexmanufacture()
    {
        $manufacturers = Manufacturer::all();
        return view('user.manufacturer', compact('manufacturers'));
    }

    public function showProductsByManufacturer($id)
    {
        $manufacturer = Manufacturer::findOrFail($id);

        $page = request()->query('page', 1);

        // Validate page number
        if (!is_numeric($page) || $page < 1 || $page > PHP_INT_MAX) {
            return redirect()->route('manufacturer.products', ['id' => $id])->with('error', 'Tham số trang không hợp lệ.');
        }

        $products = Product::where('id_manufacturer', $id)->paginate(6);

        // Check if the requested page is valid within the paginated results
        if ($products->currentPage() > $products->lastPage() && $products->lastPage() > 0) {
             return redirect()->route('manufacturer.products', ['id' => $id])->with('error', 'Tham số trang không hợp lệ.');
        }

        // Lấy thêm dữ liệu cần thiết cho layout
        $categories = Category::all();
        $manufacturers = Manufacturer::all();

        // Tính số lượng giỏ hàng và đơn hàng
        $cartCount = 0;
        $orderCount = 0;

        if (Session::get('id_user')) {
            $cartCount = Cart::where('id_user', Session::get('id_user'))->count();
            $orderCount = Order::where('id_user', Session::get('id_user'))->count();
        }

        return view('user.products_by_manufacturer', [
            'manufacturer' => $manufacturer,
            'products' => $products,
            'categorys' => $categories,
            'manufacturers' => $manufacturers,
            'cartCount' => $cartCount,
            'orderCount' => $orderCount
        ]);
    }
}
