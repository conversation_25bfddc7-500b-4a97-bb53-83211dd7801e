<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description', 'Shop thời trang hiện đại - B<PERSON> sưu tập áo quần thời trang nam nữ chất lượng cao, gi<PERSON> tốt nhất. Giao hàng toàn quốc, đổi trả miễn phí.'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords', 'shop thời trang, áo quần, thời trang nam, thời trang nữ, quần áo đẹp, giá rẻ'); ?>">
    <meta name="author" content="Shop Thời Trang">
    <meta property="og:title" content="<?php echo $__env->yieldContent('title', 'Shop Thời Trang - <PERSON><PERSON> sưu tập thời trang hiện đại'); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('meta_description', 'Shop thời trang hiện đại với bộ sưu tập đa dạng, chất lượng cao'); ?>">
    <meta property="og:image" content="<?php echo e(asset('img/logo.png')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta name="twitter:card" content="summary_large_image">
    
    <title><?php echo $__env->yieldContent('title', 'Shop Thời Trang - Bộ sưu tập thời trang hiện đại'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo e(asset('img/logo.png')); ?>">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <?php echo $__env->yieldContent('styles'); ?>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f59e0b;
            --accent-color: #10b981;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #ffffff;
        }
        
        .font-display {
            font-family: 'Playfair Display', serif;
        }
        
        /* Header Styles */
        .modern-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .top-bar {
            background: rgba(0, 0, 0, 0.1);
            padding: 8px 0;
            font-size: 14px;
        }
        
        .main-header {
            padding: 15px 0;
        }
        
        .logo img {
            height: 60px;
            width: auto;
            object-fit: contain;
        }
        
        .search-form {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .search-input {
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            font-size: 16px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .search-btn {
            border-radius: 50px;
            padding: 12px 25px;
            background: var(--secondary-color);
            border: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: #d97706;
            transform: translateY(-2px);
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .header-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .header-link:hover {
            color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .header-link i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .header-link span {
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
        }
        
        /* Navigation */
        .main-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            padding: 15px 20px;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0 5px;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background: var(--light-color);
            transform: translateY(-2px);
        }
        
        /* Alerts */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }
        
        .alert-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-actions {
                gap: 4px;
                flex-wrap: wrap;
            }

            .header-link span {
                display: none;
            }

            .header-link {
                padding: 6px;
                min-width: 40px;
            }

            .search-form {
                margin: 10px 0;
            }

            .dropdown-menu {
                min-width: 200px;
                right: 0;
                left: auto;
            }
        }
        
        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Header -->
    <header class="modern-header">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex gap-3 text-white-50">
                            <span><i class="fas fa-phone me-1"></i> +84 123 456 789</span>
                            <span><i class="fas fa-envelope me-1"></i> <EMAIL></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="#" class="text-white-50"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="text-white-50"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="text-white-50"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <!-- Logo -->
                    <div class="col-lg-2 col-md-3 col-6">
                        <a href="<?php echo e(route('home.index')); ?>" class="logo">
                            <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang" class="img-fluid">
                        </a>
                    </div>

                    <!-- Search -->
                    <div class="col-lg-6 col-md-5 col-12 order-md-2 order-3">
                        <form action="<?php echo e(route('user.searchProduct')); ?>" method="GET" class="search-form">
                            <div class="input-group">
                                <input type="text" name="keyword" class="form-control search-input" 
                                       placeholder="Tìm kiếm sản phẩm..." required>
                                <button class="btn search-btn" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Actions -->
                    <div class="col-lg-4 col-md-4 col-6 order-md-3 order-2">
                        <div class="header-actions justify-content-end">
                            <!-- Bài viết -->
                            <a href="<?php echo e(route('post.indexListPostUser')); ?>" class="header-link">
                                <i class="fas fa-newspaper"></i>
                                <span>Bài viết</span>
                            </a>

                            <!-- Nhà sản xuất -->
                            <div class="dropdown">
                                <a href="#" class="header-link dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-industry"></i>
                                    <span>Nhà sản xuất</span>
                                </a>
                                <ul class="dropdown-menu">
                                    <?php if(isset($manufacturers) && $manufacturers->count() > 0): ?>
                                        <?php $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><a class="dropdown-item" href="<?php echo e(route('manufacturer.products', $manufacturer->id_manufacturer)); ?>"><?php echo e($manufacturer->name_manufacturer); ?></a></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <li><span class="dropdown-item-text">Không có nhà sản xuất</span></li>
                                    <?php endif; ?>
                                </ul>
                            </div>

                            <?php if(Session::get('id_user')): ?>
                                <a href="<?php echo e(route('cart.indexCart')); ?>" class="header-link">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Giỏ hàng</span>
                                    <span class="badge-count" id="cartCount"><?php echo e($cartCount ?? 0); ?></span>
                                </a>
                                <a href="<?php echo e(route('order.orderIndex')); ?>" class="header-link">
                                    <i class="fas fa-file-invoice"></i>
                                    <span>Đơn hàng</span>
                                    <span class="badge-count" id="orderCount"><?php echo e($orderCount ?? 0); ?></span>
                                </a>
                                <div class="dropdown">
                                    <a href="#" class="header-link dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-user"></i>
                                        <span>Tài khoản</span>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Thông tin</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('signout')); ?>"><i class="fas fa-sign-out-alt me-2"></i>Đăng xuất</a></li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <a href="<?php echo e(route('login')); ?>" class="header-link">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>Đăng nhập</span>
                                </a>
                                <a href="/register" class="header-link">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Đăng ký</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Alerts -->
    <div class="container mt-3">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-down">
                <ul class="mb-0">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><i class="fas fa-exclamation-triangle me-2"></i><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Main Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Loading functions
        function showLoading() {
            $('#loadingOverlay').fadeIn(300);
        }

        function hideLoading() {
            $('#loadingOverlay').fadeOut(300);
        }

        // Update cart and order counts
        function updateCounts() {
            <?php if(Session::get('id_user')): ?>
                $.get('<?php echo e(route("cart.getCount")); ?>', function(data) {
                    $('#cartCount').text(data.count);
                });
                $.get('<?php echo e(route("order.getCount")); ?>', function(data) {
                    $('#orderCount').text(data.count);
                });
            <?php endif; ?>
        }

        $(document).ready(function() {
            // Hide loading on page load
            hideLoading();
            
            // Update counts
            updateCounts();
            
            // Show loading on form submit and link clicks
            $('form').on('submit', showLoading);
            $('a:not([href^="#"]):not([target="_blank"])').on('click', function() {
                if ($(this).attr('href') !== 'javascript:void(0)') {
                    showLoading();
                }
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
        });
    </script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/layouts/modern.blade.php ENDPATH**/ ?>