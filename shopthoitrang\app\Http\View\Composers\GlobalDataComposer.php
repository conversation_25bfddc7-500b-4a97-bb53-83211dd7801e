<?php

namespace App\Http\View\Composers;

use Illuminate\View\View;
use App\Models\Category;
use App\Models\Manufacturer;
use App\Models\Cart;
use App\Models\Order;
use Illuminate\Support\Facades\Session;

class GlobalDataComposer
{
    /**
     * Bind data to the view.
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        // Lấy dữ liệu chung cho tất cả view
        $categories = Category::all();
        $manufacturers = Manufacturer::all();
        
        // Tính số lượng giỏ hàng và đơn hàng
        $cartCount = 0;
        $orderCount = 0;
        
        if (Session::get('id_user')) {
            $cartCount = Cart::where('id_user', Session::get('id_user'))->count();
            $orderCount = Order::where('id_user', Session::get('id_user'))->count();
        }
        
        // Truyền dữ liệu vào view
        $view->with([
            'categorys' => $categories,
            'manufacturers' => $manufacturers,
            'cartCount' => $cartCount,
            'orderCount' => $orderCount
        ]);
    }
}
