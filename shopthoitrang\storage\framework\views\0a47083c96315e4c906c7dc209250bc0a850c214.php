<?php $__env->startSection('title', '<PERSON>ăng ký - Shop Thời Trang'); ?>
<?php $__env->startSection('meta_description', '<PERSON><PERSON>ng ký tài khoản Shop Thời Trang để trải nghiệm mua sắm tuyệt vời với nhiều ưu đãi hấp dẫn.'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .auth-section {
        min-height: 100vh;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        display: flex;
        align-items: center;
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .auth-left {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 3rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }
    
    .auth-right {
        padding: 3rem;
    }
    
    .auth-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 2rem;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
    }
    
    .auth-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--dark-color);
    }
    
    .auth-subtitle {
        color: var(--text-color);
        margin-bottom: 2rem;
    }
    
    .form-floating {
        margin-bottom: 1.5rem;
    }
    
    .form-floating .form-control {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 1rem 0.75rem;
        height: auto;
        transition: all 0.3s ease;
    }
    
    .form-floating .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .form-floating label {
        color: var(--text-color);
        opacity: 0.7;
    }
    
    .btn-auth {
        width: 100%;
        padding: 1rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-auth:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
    }
    
    .password-strength {
        margin-top: 0.5rem;
        font-size: 0.875rem;
    }
    
    .strength-bar {
        height: 4px;
        border-radius: 2px;
        background: var(--border-color);
        margin-top: 0.5rem;
        overflow: hidden;
    }
    
    .strength-fill {
        height: 100%;
        transition: all 0.3s ease;
        border-radius: 2px;
    }
    
    .strength-weak { background: #ef4444; width: 25%; }
    .strength-fair { background: #f59e0b; width: 50%; }
    .strength-good { background: #10b981; width: 75%; }
    .strength-strong { background: #059669; width: 100%; }
    
    .terms-check {
        margin: 1.5rem 0;
    }
    
    .terms-check .form-check {
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .terms-check .form-check-input {
        border-radius: 4px;
        border: 2px solid var(--border-color);
        margin-top: 0.25rem;
    }
    
    .terms-check .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .terms-check .form-check-label {
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    .terms-check a {
        color: var(--primary-color);
        text-decoration: none;
    }
    
    .terms-check a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }
    
    .auth-links {
        text-align: center;
        margin-top: 2rem;
    }
    
    .auth-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }
    
    .auth-links a:hover {
        color: var(--secondary-color);
    }
    
    @media (max-width: 768px) {
        .auth-left {
            display: none;
        }
        
        .auth-right {
            padding: 2rem;
        }
        
        .auth-title {
            font-size: 1.75rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="auth-section">
    <div class="container">
        <div class="auth-card" data-aos="fade-up">
            <div class="row g-0">
                <div class="col-lg-5">
                    <div class="auth-left">
                        <div class="auth-logo">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h3 class="font-display">Tham gia cùng chúng tôi!</h3>
                        <p>Tạo tài khoản để trải nghiệm mua sắm tuyệt vời với nhiều ưu đãi hấp dẫn</p>
                        <div class="mt-4">
                            <p class="mb-2">Đã có tài khoản?</p>
                            <a href="<?php echo e(route('login')); ?>" class="btn btn-outline-light">Đăng nhập ngay</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7">
                    <div class="auth-right">
                        <h2 class="auth-title font-display">Đăng ký</h2>
                        <p class="auth-subtitle">Tạo tài khoản mới để bắt đầu mua sắm</p>
                        
                        <form action="<?php echo e(route('user.cus_register')); ?>" method="POST" id="registerForm">
                            <?php echo csrf_field(); ?>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" 
                                               class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="name" 
                                               name="name" 
                                               placeholder="Họ và tên"
                                               value="<?php echo e(old('name')); ?>"
                                               required>
                                        <label for="name">Họ và tên</label>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" 
                                               class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="email" 
                                               name="email" 
                                               placeholder="Email"
                                               value="<?php echo e(old('email')); ?>"
                                               required>
                                        <label for="email">Email</label>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" 
                                               class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="password" 
                                               name="password" 
                                               placeholder="Mật khẩu"
                                               required>
                                        <label for="password">Mật khẩu</label>
                                        <div class="password-strength">
                                            <div class="strength-text">Độ mạnh mật khẩu: <span id="strengthText">Yếu</span></div>
                                            <div class="strength-bar">
                                                <div class="strength-fill" id="strengthFill"></div>
                                            </div>
                                        </div>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" 
                                               class="form-control <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="password_confirmation" 
                                               name="password_confirmation" 
                                               placeholder="Xác nhận mật khẩu"
                                               required>
                                        <label for="password_confirmation">Xác nhận mật khẩu</label>
                                        <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" 
                                               class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="phone" 
                                               name="phone" 
                                               placeholder="Số điện thoại"
                                               value="<?php echo e(old('phone')); ?>"
                                               required>
                                        <label for="phone">Số điện thoại</label>
                                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" 
                                               class="form-control <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                               id="address" 
                                               name="address" 
                                               placeholder="Địa chỉ"
                                               value="<?php echo e(old('address')); ?>"
                                               required>
                                        <label for="address">Địa chỉ</label>
                                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback">
                                                <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                            </div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="terms-check">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                    <label class="form-check-label" for="terms">
                                        Tôi đồng ý với <a href="#" onclick="alert('Điều khoản sử dụng đang được cập nhật.'); return false;">Điều khoản sử dụng</a> 
                                        và <a href="#" onclick="alert('Chính sách bảo mật đang được cập nhật.'); return false;">Chính sách bảo mật</a> 
                                        của Shop Thời Trang
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-auth" id="registerBtn">
                                <i class="fas fa-user-plus me-2"></i>Đăng ký
                            </button>
                        </form>
                        
                        <div class="auth-links">
                            <p class="mb-2">Đã có tài khoản? <a href="<?php echo e(route('login')); ?>">Đăng nhập ngay</a></p>
                            <p class="mb-0"><a href="<?php echo e(route('home.index')); ?>">← Quay lại trang chủ</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Auto focus name input
        $('#name').focus();
        
        // Password strength checker
        $('#password').on('input', function() {
            const password = $(this).val();
            const strength = checkPasswordStrength(password);
            updatePasswordStrength(strength);
        });
        
        // Password confirmation checker
        $('#password_confirmation').on('input', function() {
            const password = $('#password').val();
            const confirmation = $(this).val();
            
            if (confirmation && password !== confirmation) {
                $(this).addClass('is-invalid');
                if (!$(this).next('.invalid-feedback').length) {
                    $(this).after('<div class="invalid-feedback">Mật khẩu xác nhận không khớp</div>');
                }
            } else {
                $(this).removeClass('is-invalid');
                $(this).next('.invalid-feedback').remove();
            }
        });
        
        // Form validation
        $('#registerForm').on('submit', function(e) {
            const password = $('#password').val();
            const confirmation = $('#password_confirmation').val();
            const terms = $('#terms').is(':checked');
            
            if (password !== confirmation) {
                e.preventDefault();
                showToast('Mật khẩu xác nhận không khớp!', 'error');
                return false;
            }
            
            if (!terms) {
                e.preventDefault();
                showToast('Vui lòng đồng ý với điều khoản sử dụng!', 'error');
                return false;
            }
            
            // Show loading
            const btn = $('#registerBtn');
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng ký...');
            btn.prop('disabled', true);
            
            // Reset button after 10 seconds if no response
            setTimeout(function() {
                btn.html(originalText);
                btn.prop('disabled', false);
            }, 10000);
        });
    });
    
    function checkPasswordStrength(password) {
        let score = 0;
        
        if (password.length >= 8) score++;
        if (/[a-z]/.test(password)) score++;
        if (/[A-Z]/.test(password)) score++;
        if (/[0-9]/.test(password)) score++;
        if (/[^A-Za-z0-9]/.test(password)) score++;
        
        return score;
    }
    
    function updatePasswordStrength(score) {
        const strengthText = $('#strengthText');
        const strengthFill = $('#strengthFill');
        
        strengthFill.removeClass('strength-weak strength-fair strength-good strength-strong');
        
        switch(score) {
            case 0:
            case 1:
                strengthText.text('Yếu');
                strengthFill.addClass('strength-weak');
                break;
            case 2:
                strengthText.text('Trung bình');
                strengthFill.addClass('strength-fair');
                break;
            case 3:
            case 4:
                strengthText.text('Tốt');
                strengthFill.addClass('strength-good');
                break;
            case 5:
                strengthText.text('Rất mạnh');
                strengthFill.addClass('strength-strong');
                break;
        }
    }
    
    // Toast notification function
    function showToast(message, type = 'success') {
        const toast = $(`
            <div class="toast-notification ${type}">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            </div>
        `);
        
        $('body').append(toast);
        
        setTimeout(() => {
            toast.addClass('show');
        }, 100);
        
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/auth/register-modern.blade.php ENDPATH**/ ?>