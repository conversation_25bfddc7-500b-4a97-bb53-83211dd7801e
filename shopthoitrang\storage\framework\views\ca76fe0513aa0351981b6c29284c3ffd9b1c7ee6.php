<?php $__env->startSection('title', '<PERSON>ăng nhập - Shop Thời Trang'); ?>
<?php $__env->startSection('meta_description', '<PERSON><PERSON>ng nhập và<PERSON> tà<PERSON>n Shop Thời Trang để mua sắm và quản lý đơn hàng của bạn.'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .auth-section {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        padding: 2rem 0;
    }
    
    .auth-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
        max-width: 900px;
        margin: 0 auto;
    }
    
    .auth-left {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }
    
    .auth-right {
        padding: 3rem;
    }
    
    .auth-logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 2rem;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
    }
    
    .auth-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: var(--dark-color);
    }
    
    .auth-subtitle {
        color: var(--text-color);
        margin-bottom: 2rem;
    }
    
    .form-floating {
        margin-bottom: 1.5rem;
    }
    
    .form-floating .form-control {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 1rem 0.75rem;
        height: auto;
        transition: all 0.3s ease;
    }
    
    .form-floating .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .form-floating label {
        color: var(--text-color);
        opacity: 0.7;
    }
    
    .btn-auth {
        width: 100%;
        padding: 1rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        border: none;
    }
    
    .btn-auth:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
    }
    
    .divider {
        text-align: center;
        margin: 2rem 0;
        position: relative;
    }
    
    .divider::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: var(--border-color);
    }
    
    .divider span {
        background: white;
        padding: 0 1rem;
        color: var(--text-color);
        opacity: 0.7;
    }
    
    .auth-links {
        text-align: center;
        margin-top: 2rem;
    }
    
    .auth-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;
    }
    
    .auth-links a:hover {
        color: var(--secondary-color);
    }
    
    .remember-forgot {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-check-input {
        border-radius: 4px;
        border: 2px solid var(--border-color);
    }
    
    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }
    
    .forgot-link {
        color: var(--primary-color);
        text-decoration: none;
        font-size: 0.9rem;
    }
    
    .forgot-link:hover {
        color: var(--secondary-color);
    }
    
    @media (max-width: 768px) {
        .auth-left {
            display: none;
        }
        
        .auth-right {
            padding: 2rem;
        }
        
        .auth-title {
            font-size: 1.75rem;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<section class="auth-section">
    <div class="container">
        <div class="auth-card" data-aos="fade-up">
            <div class="row g-0">
                <div class="col-lg-5">
                    <div class="auth-left">
                        <div class="auth-logo">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <h3 class="font-display">Chào mừng trở lại!</h3>
                        <p>Đăng nhập để tiếp tục mua sắm và khám phá những sản phẩm thời trang mới nhất</p>
                        <div class="mt-4">
                            <p class="mb-2">Chưa có tài khoản?</p>
                            <a href="/register" class="btn btn-outline-light">Đăng ký ngay</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-7">
                    <div class="auth-right">
                        <h2 class="auth-title font-display">Đăng nhập</h2>
                        <p class="auth-subtitle">Nhập thông tin để đăng nhập vào tài khoản của bạn</p>
                        
                        <form action="<?php echo e(route('user.cus_login')); ?>" method="POST" id="loginForm">
                            <?php echo csrf_field(); ?>
                            
                            <div class="form-floating">
                                <input type="email" 
                                       class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="email" 
                                       name="email" 
                                       placeholder="Email"
                                       value="<?php echo e(old('email')); ?>"
                                       required>
                                <label for="email">Email</label>
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="form-floating">
                                <input type="password" 
                                       class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="password" 
                                       name="password" 
                                       placeholder="Mật khẩu"
                                       required>
                                <label for="password">Mật khẩu</label>
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-circle me-1"></i><?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div class="remember-forgot">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        Ghi nhớ đăng nhập
                                    </label>
                                </div>
                                <a href="#" class="forgot-link" onclick="alert('Chức năng quên mật khẩu đang được phát triển.'); return false;">
                                    Quên mật khẩu?
                                </a>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-auth" id="loginBtn">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </button>
                        </form>
                        
                        <div class="divider">
                            <span>hoặc</span>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="alert('Chức năng đăng nhập Google đang được phát triển.')">
                                <i class="fab fa-google me-2"></i>Đăng nhập với Google
                            </button>
                            <button class="btn btn-outline-primary" onclick="alert('Chức năng đăng nhập Facebook đang được phát triển.')">
                                <i class="fab fa-facebook me-2"></i>Đăng nhập với Facebook
                            </button>
                        </div>
                        
                        <div class="auth-links">
                            <p class="mb-2">Chưa có tài khoản? <a href="/register">Đăng ký ngay</a></p>
                            <p class="mb-0"><a href="<?php echo e(route('home.index')); ?>">← Quay lại trang chủ</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Auto focus email input
        $('#email').focus();
        
        // Form validation
        $('#loginForm').on('submit', function(e) {
            const email = $('#email').val().trim();
            const password = $('#password').val().trim();
            
            if (!email || !password) {
                e.preventDefault();
                showToast('Vui lòng nhập đầy đủ thông tin!', 'error');
                return false;
            }
            
            // Show loading
            const btn = $('#loginBtn');
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng nhập...');
            btn.prop('disabled', true);
            
            // Reset button after 10 seconds if no response
            setTimeout(function() {
                btn.html(originalText);
                btn.prop('disabled', false);
            }, 10000);
        });
        
        // Enter key support
        $('#password').on('keypress', function(e) {
            if (e.which === 13) {
                $('#loginForm').submit();
            }
        });
    });
    
    // Toast notification function
    function showToast(message, type = 'success') {
        const toast = $(`
            <div class="toast-notification ${type}">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            </div>
        `);
        
        $('body').append(toast);
        
        setTimeout(() => {
            toast.addClass('show');
        }, 100);
        
        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.modern', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/auth/login-modern.blade.php ENDPATH**/ ?>