

<?php $__env->startSection('content'); ?>

<?php if(session('error')): ?>
    <div class="alert alert-danger">
        <?php echo e(session('error')); ?>

    </div>
<?php endif; ?>

<div class="container py-5">
    <h2 class="mb-4 text-center">K<PERSON><PERSON> quả tìm kiếm cho: <strong>"<?php echo e($keyword); ?>"</strong></h2>

    <div class="row">
        <!-- B<PERSON> lọc bên trái -->
        <div class="col-md-3">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-4">Bộ lọc sản phẩm</h5>
                    <form action="<?php echo e(route('user.filterProduct')); ?>" method="GET" id="filterForm">
                        <input type="hidden" name="keyword" value="<?php echo e($keyword ?? ''); ?>">

                        <!-- <PERSON>h mục sản phẩm -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3"><PERSON><PERSON> mục</h6>
                            <select name="category" class="form-select" onchange="this.form.submit()">
                                <option value="">Tất cả danh mục</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id_category); ?>" <?php echo e(request('category') == $category->id_category ? 'selected' : ''); ?>>
                                        <?php echo e($category->name_category); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Hãng sản xuất -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Hãng sản xuất</h6>
                            <select name="manufacturer" class="form-select" onchange="this.form.submit()">
                                <option value="">Tất cả hãng</option>
                                <?php $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($manufacturer->id_manufacturer); ?>" <?php echo e(request('manufacturer') == $manufacturer->id_manufacturer ? 'selected' : ''); ?>>
                                        <?php echo e($manufacturer->name_manufacturer); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <!-- Khoảng giá -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Khoảng giá (VNĐ)</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" name="price_min" class="form-control form-control-sm" 
                                        placeholder="Từ" value="<?php echo e(request('price_min')); ?>" min="0">
                                </div>
                                <div class="col-6">
                                    <input type="number" name="price_max" class="form-control form-control-sm" 
                                        placeholder="Đến" value="<?php echo e(request('price_max')); ?>" min="0">
                                </div>
                            </div>
                        </div>

                        <!-- Số lượt mua -->
                        <div class="mb-4">
                            <h6 class="fw-bold mb-3">Số lượt mua</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" name="purchased_min" class="form-control form-control-sm" 
                                        placeholder="Từ" value="<?php echo e(request('purchased_min')); ?>" min="0">
                                </div>
                                <div class="col-6">
                                    <input type="number" name="purchased_max" class="form-control form-control-sm" 
                                        placeholder="Đến" value="<?php echo e(request('purchased_max')); ?>" min="0">
                                </div>
                            </div>
                        </div>

                        <!-- Nút lọc -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Lọc sản phẩm</button>
                            <a href="<?php echo e(route('user.searchProduct', ['keyword' => $keyword ?? ''])); ?>" class="btn btn-outline-secondary">
                                Xóa bộ lọc
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Danh sách sản phẩm bên phải -->
        <div class="col-md-9">
            <?php if($products->isEmpty()): ?>
            <div class="alert alert-warning text-center">
                Không tìm thấy sản phẩm nào phù hợp với tiêu chí lọc.
            </div>
            <?php else: ?>
            <div class="row">
                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4 col-sm-6 mb-4">
                    <div class="card h-100 shadow-sm border-0">
                        <div class="text-center p-3" style="background-color: #f9f9f9;">
                            <img src="<?php echo e(asset('uploads/productimage/' . $product->image_address_product)); ?>"
                                alt="<?php echo e($product->name_product); ?>"
                                class="img-fluid" style="max-height: 200px; object-fit: contain;">
                        </div>
                        <div class="card-body text-center">
                            <h5 class="card-title mb-2"><?php echo e($product->name_product); ?></h5>
                            <p class="card-text text-danger mb-3"><?php echo e(number_format($product->price_product, 0, ',', '.')); ?> VND</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">Đã bán: <?php echo e($product->purchased); ?></small>
                                <a href="<?php echo e(route('product.indexDetailproduct', ['id' => $product->id_product])); ?>"
                                    class="btn btn-outline-primary btn-sm">Xem chi tiết</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <div class="d-flex justify-content-center mt-4">
                <?php echo e($products->appends(request()->query())->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.form-check {
    margin-bottom: 0.5rem;
}
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
.card {
    transition: transform 0.2s;
}
.card:hover {
    transform: translateY(-5px);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validate giá trị nhập vào
    const priceInputs = document.querySelectorAll('input[type="number"]');
    priceInputs.forEach(input => {
        input.addEventListener('input', function() {
            if (this.value < 0) {
                this.value = 0;
            }
        });
    });

    // Tự động submit form khi thay đổi radio button
    const radioInputs = document.querySelectorAll('input[type="radio"]');
    radioInputs.forEach(input => {
        input.addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('user.dashboard_user', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/user/searchProduct.blade.php ENDPATH**/ ?>