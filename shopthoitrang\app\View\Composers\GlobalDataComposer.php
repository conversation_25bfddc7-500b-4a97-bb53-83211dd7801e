<?php

namespace App\View\Composers;

use Illuminate\View\View;
use App\Models\Category;
use App\Models\Manufacturer;

class GlobalDataComposer
{
    /**
     * Bind data to the view.
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        try {
            // Get all categories
            $categories = Category::orderBy('name_category', 'asc')->get();
            
            // Get all manufacturers
            $manufacturers = Manufacturer::orderBy('name_manufacturer', 'asc')->get();
            
            // Share data with view
            $view->with([
                'categorys' => $categories,
                'manufacturers' => $manufacturers
            ]);
        } catch (\Exception $e) {
            // Fallback to empty collections if database error
            $view->with([
                'categorys' => collect(),
                'manufacturers' => collect()
            ]);
        }
    }
}
