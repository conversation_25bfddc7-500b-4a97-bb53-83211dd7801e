
<?php $__env->startSection('title', 'Quản lý đơn hàng'); ?>
<?php $__env->startSection('page-title', 'Quản lý đơn hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Card chính -->
    <div class="card card-primary card-outline">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">
                    <i class="fas fa-shopping-cart mr-2"></i>Danh sách đơn hàng
                </h3>
                <div class="card-tools">
                    <span class="badge badge-info">
                        Tổng: <?php echo e(method_exists($order, 'total') ? $order->total() : count($order)); ?> đơn hàng
                    </span>
                </div>
            </div>
        </div>

        <!-- Thanh tìm kiếm -->
        <div class="card-body pb-0">
            <form action="<?php echo e(route('admin.adminSearchOrder')); ?>" method="GET" class="mb-3">
                <div class="input-group">
                    <input type="text" name="id" class="form-control"
                           placeholder="Tìm kiếm theo mã đơn hàng hoặc mã người dùng..."
                           value="<?php echo e(request('id')); ?>">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                        <?php if(request('id')): ?>
                        <a href="<?php echo e(route('admin.orderindexAdmin')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Xóa
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </form>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 100px;" class="text-center">Mã đơn hàng</th>
                        <th style="width: 100px;" class="text-center">Mã khách hàng</th>
                        <th style="width: 150px;" class="text-right">Tổng tiền</th>
                        <th>Địa chỉ giao hàng</th>
                        <th style="width: 120px;" class="text-center">Trạng thái</th>
                        <th style="width: 150px;" class="text-center">Ngày đặt</th>
                        <th style="width: 140px;" class="text-center">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $order; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $orders): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td class="text-center">
                            <span class="badge badge-primary">#<?php echo e($orders->id_order); ?></span>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-secondary"><?php echo e($orders->id_user); ?></span>
                        </td>
                        <td class="text-right font-weight-bold text-success">
                            <?php echo e(number_format($orders->total_order, 0, ',', '.')); ?>đ
                        </td>
                        <td>
                            <div class="address-info">
                                <i class="fas fa-map-marker-alt text-muted mr-1"></i>
                                <?php echo e(Str::limit($orders->address, 50)); ?>

                                <?php if(strlen($orders->address) > 50): ?>
                                    <button class="btn btn-link btn-sm p-0 ml-1"
                                            onclick="showFullAddress('<?php echo e(addslashes($orders->address)); ?>')">
                                        <i class="fas fa-expand-alt"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-warning">
                                <i class="fas fa-clock mr-1"></i>Chờ xử lý
                            </span>
                        </td>
                        <td class="text-center text-muted">
                            <div><?php echo e($orders->created_at->format('d/m/Y')); ?></div>
                            <small><?php echo e($orders->created_at->format('H:i')); ?></small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="<?php echo e(route('admin.adminDetailsOrderIndex', ['id_order' => $orders->id_order])); ?>"
                                   class="btn btn-sm btn-info"
                                   title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button"
                                        class="btn btn-sm btn-danger btn-cancel-order"
                                        data-order-id="<?php echo e($orders->id_order); ?>"
                                        data-order-total="<?php echo e(number_format($orders->total_order, 0, ',', '.')); ?>"
                                        title="Hủy đơn hàng">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                <p class="mb-0">Không có đơn hàng nào</p>
                                <?php if(request('id')): ?>
                                    <small>Không tìm thấy kết quả cho "<?php echo e(request('id')); ?>"</small>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if($order->hasPages()): ?>
        <div class="card-footer clearfix">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Hiển thị <?php echo e($order->firstItem() ?? 1); ?> đến <?php echo e($order->lastItem() ?? count($order)); ?>

                    trong tổng số <?php echo e($order->total()); ?> đơn hàng
                </div>
                <div>
                    <?php echo e($order->links()); ?>

                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal xem địa chỉ đầy đủ -->
<div class="modal fade" id="addressModal" tabindex="-1" role="dialog" aria-labelledby="addressModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addressModalLabel">Địa chỉ giao hàng</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="addressModalBody">
                <!-- Địa chỉ sẽ được load bằng JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .address-info {
        max-width: 300px;
        word-wrap: break-word;
    }

    .table td {
        vertical-align: middle;
    }

    .btn-link {
        color: #007bff;
        text-decoration: none;
    }

    .btn-link:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .badge {
        font-size: 0.75em;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Auto focus vào ô tìm kiếm
        $('input[name="id"]').focus();

        // Xử lý nút hủy đơn hàng
        $('.btn-cancel-order').on('click', function() {
            const orderId = $(this).data('order-id');
            const orderTotal = $(this).data('order-total');

            Swal.fire({
                title: 'Xác nhận hủy đơn hàng',
                html: `
                    <p>Bạn có chắc chắn muốn hủy đơn hàng <strong>#${orderId}</strong>?</p>
                    <p class="text-muted">Tổng tiền: <strong>${orderTotal}đ</strong></p>
                    <p class="text-warning"><i class="fas fa-exclamation-triangle"></i> Hành động này không thể hoàn tác!</p>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Có, hủy đơn!',
                cancelButtonText: 'Không, giữ lại'
            }).then((result) => {
                if (result.isConfirmed) {
                    showLoading();
                    window.location.href = `<?php echo e(route('admin.adminDetailsOrderDelete', '')); ?>?id_order=${orderId}`;
                }
            });
        });

        // Hiệu ứng chuyển trang mượt mà
        $(document).on('click', '.pagination a', function(event) {
            event.preventDefault();
            var url = $(this).attr('href');
            showLoading();
            setTimeout(function() {
                window.location.href = url;
            }, 300);
        });
    });

    // Hàm hiển thị địa chỉ đầy đủ
    function showFullAddress(address) {
        $('#addressModalBody').html(`<p class="mb-0"><i class="fas fa-map-marker-alt text-primary mr-2"></i>${address}</p>`);
        $('#addressModal').modal('show');
    }
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/order/orderindex.blade.php ENDPATH**/ ?>