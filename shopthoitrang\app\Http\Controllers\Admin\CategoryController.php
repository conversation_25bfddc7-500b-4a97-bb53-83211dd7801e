<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;

class CategoryController extends Controller
{
    public function indexCategory(Request $request){
        $categories = \App\Models\Category::orderBy('created_at', 'desc')->paginate(10);
        return view('admin.category.categoryIndex', ['categories' => $categories]);
    }

    public function indexcreateCategory(){
        return view('admin.category.categoryCreateIndex');
    }
    
    public function createCategory(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:categories,name_category'
        ], [
            'name.required' => 'Vui lòng nhập tên danh mục',
            'name.max' => 'Tên danh mục không quá 255 ký tự',
            'name.unique' => 'Tên danh mục đã tồn tại'
        ]);

        try {
            \App\Models\Category::create([
                'name_category' => $request->name
            ]);
            return redirect()->route('category.index')->with('success', 'Thêm danh mục thành công!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Có lỗi xảy ra: ' . $e->getMessage())->withInput();
        }
    }

    public function indexupdateCategory($id)
    {
        $cate = \App\Models\Category::where('id_category', $id)->first();
        if (!$cate) {
            abort(404, 'Không tìm thấy danh mục');
        }
        return view('admin.category.categoryUpdateIndex', ['category' => $cate]);
    }

    public function updateCategory(Request $request)
    {
        $input = $request->all();
        $input_id = $input['id'];
        $cate = Category::where('id_category', $input_id)->first();
        $cate->name_category = $input['name'];

        $cate->save();
        return redirect('category');
    }

    public function deleteCategory($id)
    {
        $deleted = \App\Models\Category::where('id_category', $id)->delete();
        if ($deleted) {
            return redirect('category')->with('success', 'Xóa danh mục thành công!');
        } else {
            return redirect('category')->with('error', 'Không tìm thấy danh mục để xóa!');
        }
    }
}
