@extends('layouts.app')

@section('title', 'Trang chủ - Shop Thời Trang')
@section('meta_description', 'Shop thời trang hiện đại với bộ sưu tập đa dạng, chất lư<PERSON> cao. <PERSON><PERSON> quần thời trang nam nữ, g<PERSON><PERSON>, giao hàng toàn quốc.')
@section('meta_keywords', 'shop thời trang, <PERSON><PERSON> quần, thời trang nam, thời trang nữ, quần á<PERSON> đẹp, gi<PERSON> rẻ, bộ sưu tập mới')

@section('styles')
<style>
    /* Hero Section */
    .hero-section {
        position: relative;
        overflow: hidden;
    }
    
    .hero-slide {
        min-height: 80vh;
        display: flex;
        align-items: center;
        color: white;
        position: relative;
    }
    
    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
    }
    
    .hero-actions .btn {
        padding: 15px 30px;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
    }
    
    .hero-actions .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    
    .hero-image img {
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }
    
    /* Features Section */
    .features-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }
    
    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .feature-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 2rem;
    }
    
    /* Section Headers */
    .section-header {
        margin-bottom: 3rem;
    }
    
    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 1rem;
    }
    
    .section-subtitle {
        font-size: 1.1rem;
        color: var(--text-color);
        opacity: 0.8;
    }
    
    /* Product Cards */
    .product-card.modern {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .product-card.modern:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }
    
    .product-image {
        position: relative;
        overflow: hidden;
        height: 250px;
    }
    
    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .product-image img {
        transform: scale(1.1);
    }
    
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .product-card:hover .product-overlay {
        opacity: 1;
    }
    
    .product-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: var(--secondary-color);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        z-index: 2;
    }
    
    .product-info {
        padding: 1.5rem;
    }
    
    .product-category {
        color: var(--primary-color);
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: 0.5rem;
    }
    
    .product-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 1rem;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .product-price {
        margin-bottom: 1rem;
    }
    
    .current-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--accent-color);
    }
    
    .add-to-cart-btn {
        border-radius: 50px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .add-to-cart-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
    }
    
    /* All Products Section */
    .all-products-section {
        padding: 5rem 0;
    }
    
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }
    
    /* Swiper Customization */
    .swiper-pagination-bullet {
        background: var(--primary-color);
        opacity: 0.3;
    }
    
    .swiper-pagination-bullet-active {
        opacity: 1;
        background: var(--secondary-color);
    }
    
    .swiper-button-next,
    .swiper-button-prev {
        color: var(--primary-color);
        background: white;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 20px;
    }
    
    /* Responsive */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }
        
        .section-title {
            font-size: 2rem;
        }
        
        .product-image {
            height: 200px;
        }
    }
</style>
@endsection

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slider swiper">
        <div class="swiper-wrapper">
            <div class="swiper-slide hero-slide" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6" data-aos="fade-right">
                            <h1 class="hero-title font-display">Bộ Sưu Tập Thời Trang Mới</h1>
                            <p class="hero-subtitle">Khám phá những xu hướng thời trang mới nhất với chất lượng cao và giá cả hợp lý</p>
                            <div class="hero-actions">
                                <a href="#new-products" class="btn btn-primary btn-lg me-3">Khám phá ngay</a>
                                <a href="{{ route('user.filterProduct') }}" class="btn btn-outline-light btn-lg">Xem tất cả</a>
                            </div>
                        </div>
                        <div class="col-lg-6" data-aos="fade-left">
                            <div class="hero-image">
                                <img src="{{ asset('img/logo.png') }}" alt="Thời trang" class="img-fluid">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="swiper-slide hero-slide" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-lg-6" data-aos="fade-right">
                            <h1 class="hero-title font-display">Thời Trang Nam Hiện Đại</h1>
                            <p class="hero-subtitle">Phong cách lịch lãm, sang trọng cho quý ông thành đạt</p>
                            <div class="hero-actions">
                                <a href="#new-products" class="btn btn-primary btn-lg me-3">Mua ngay</a>
                                <a href="{{ route('user.filterProduct') }}" class="btn btn-outline-light btn-lg">Tìm hiểu thêm</a>
                            </div>
                        </div>
                        <div class="col-lg-6" data-aos="fade-left">
                            <div class="hero-image">
                                <img src="{{ asset('img/logo.png') }}" alt="Thời trang nam" class="img-fluid">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="100">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h5>Giao hàng nhanh</h5>
                    <p>Giao hàng toàn quốc trong 24h</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="200">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-undo-alt"></i>
                    </div>
                    <h5>Đổi trả miễn phí</h5>
                    <p>Đổi trả trong vòng 30 ngày</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="300">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5>Bảo hành chất lượng</h5>
                    <p>Cam kết chất lượng 100%</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6" data-aos="fade-up" data-aos-delay="400">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5>Hỗ trợ 24/7</h5>
                    <p>Tư vấn và hỗ trợ mọi lúc</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- New Products Section -->
<section id="new-products" class="new-products-section py-5 bg-light">
    <div class="container">
        <div class="section-header text-center mb-5" data-aos="fade-up">
            <h2 class="section-title font-display">Sản Phẩm Mới Nhất</h2>
            <p class="section-subtitle">Khám phá những sản phẩm thời trang mới nhất và hot nhất</p>
        </div>

        <div class="products-slider swiper" data-aos="fade-up" data-aos-delay="200">
            <div class="swiper-wrapper">
                @foreach($get6newproducts as $product)
                <div class="swiper-slide">
                    <div class="product-card modern">
                        <div class="product-image">
                            <img src="{{ asset('uploads/productimage/' . $product->image_address_product) }}"
                                 alt="{{ $product->name_product }}" class="img-fluid">
                            <div class="product-overlay">
                                <a href="{{ route('product.indexDetailproduct', ['id' => $product->id_product]) }}"
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye"></i> Xem chi tiết
                                </a>
                            </div>
                            <div class="product-badge">Mới</div>
                        </div>
                        <div class="product-info">
                            <div class="product-category">
                                @foreach($productsWithCategorys as $categoryProduct)
                                    @if($categoryProduct->id_category == $product->id_category)
                                        {{ $categoryProduct->name_category }}
                                        @break
                                    @endif
                                @endforeach
                            </div>
                            <h5 class="product-name">{{ $product->name_product }}</h5>
                            <div class="product-price">
                                <span class="current-price">{{ number_format($product->price_product, 0, ',', '.') }}đ</span>
                            </div>
                            @if(Session::get('id_user'))
                            <form action="{{ route('cart.addCard') }}" method="POST" class="add-to-cart-form">
                                @csrf
                                <input type="hidden" name="id_product" value="{{ $product->id_product }}">
                                <input type="hidden" name="quantity" value="1">
                                <button type="submit" class="btn btn-primary w-100 add-to-cart-btn">
                                    <i class="fas fa-shopping-cart me-2"></i>Thêm vào giỏ
                                </button>
                            </form>
                            @else
                            <a href="{{ route('login') }}" class="btn btn-outline-primary w-100 add-to-cart-btn">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để mua
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            <div class="swiper-pagination"></div>
        </div>
    </div>
</section>

<!-- All Products Section -->
<section class="all-products-section">
    <div class="container">
        <div class="section-header text-center" data-aos="fade-up">
            <h2 class="section-title font-display">Tất Cả Sản Phẩm</h2>
            <p class="section-subtitle">Bộ sưu tập đa dạng với nhiều lựa chọn phong cách</p>
        </div>

        <div class="products-grid" data-aos="fade-up" data-aos-delay="200">
            @foreach($products as $product)
            <div class="product-card modern">
                <div class="product-image">
                    <img src="{{ asset('uploads/productimage/' . $product->image_address_product) }}"
                         alt="{{ $product->name_product }}" class="img-fluid">
                    <div class="product-overlay">
                        <a href="{{ route('product.indexDetailproduct', ['id' => $product->id_product]) }}"
                           class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> Xem chi tiết
                        </a>
                    </div>
                </div>
                <div class="product-info">
                    <div class="product-category">
                        @foreach($productsWithCategorys as $categoryProduct)
                            @if($categoryProduct->id_category == $product->id_category)
                                {{ $categoryProduct->name_category }}
                                @break
                            @endif
                        @endforeach
                    </div>
                    <h5 class="product-name">{{ $product->name_product }}</h5>
                    <div class="product-price">
                        <span class="current-price">{{ number_format($product->price_product, 0, ',', '.') }}đ</span>
                    </div>
                    @if(Session::get('id_user'))
                    <form action="{{ route('cart.addCard') }}" method="POST" class="add-to-cart-form">
                        @csrf
                        <input type="hidden" name="id_product" value="{{ $product->id_product }}">
                        <input type="hidden" name="quantity" value="1">
                        <button type="submit" class="btn btn-primary w-100 add-to-cart-btn">
                            <i class="fas fa-shopping-cart me-2"></i>Thêm vào giỏ
                        </button>
                    </form>
                    @else
                    <a href="{{ route('login') }}" class="btn btn-outline-primary w-100 add-to-cart-btn">
                        <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập để mua
                    </a>
                    @endif
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($products->hasPages())
        <div class="d-flex justify-content-center mt-5" data-aos="fade-up">
            {{ $products->links('pagination::bootstrap-4') }}
        </div>
        @endif
    </div>
</section>

<!-- Footer -->
<footer class="bg-dark text-white py-5">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <h5 class="font-display mb-3">Shop Thời Trang</h5>
                <p>Chuyên cung cấp các sản phẩm thời trang chất lượng cao với giá cả hợp lý. Phong cách hiện đại, xu hướng mới nhất.</p>
                <div class="d-flex gap-3">
                    <a href="#" class="text-white"><i class="fab fa-facebook fa-lg"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-instagram fa-lg"></i></a>
                    <a href="#" class="text-white"><i class="fab fa-youtube fa-lg"></i></a>
                </div>
            </div>
            <div class="col-lg-2 col-md-6">
                <h6 class="mb-3">Liên kết</h6>
                <ul class="list-unstyled">
                    <li><a href="{{ route('home.index') }}" class="text-white-50">Trang chủ</a></li>
                    <li><a href="{{ route('user.filterProduct') }}" class="text-white-50">Sản phẩm</a></li>
                    <li><a href="{{ route('post.indexListPostUser') }}" class="text-white-50">Bài viết</a></li>
                    <li><a href="#" class="text-white-50">Liên hệ</a></li>
                </ul>
            </div>
            <div class="col-lg-3 col-md-6">
                <h6 class="mb-3">Hỗ trợ</h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-white-50">Chính sách đổi trả</a></li>
                    <li><a href="#" class="text-white-50">Hướng dẫn mua hàng</a></li>
                    <li><a href="#" class="text-white-50">Chăm sóc khách hàng</a></li>
                    <li><a href="#" class="text-white-50">FAQ</a></li>
                </ul>
            </div>
            <div class="col-lg-3 col-md-6">
                <h6 class="mb-3">Liên hệ</h6>
                <ul class="list-unstyled">
                    <li class="text-white-50"><i class="fas fa-phone me-2"></i>+84 123 456 789</li>
                    <li class="text-white-50"><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    <li class="text-white-50"><i class="fas fa-map-marker-alt me-2"></i>123 Đường ABC, TP.HCM</li>
                </ul>
            </div>
        </div>
        <hr class="my-4">
        <div class="text-center">
            <p class="mb-0 text-white-50">&copy; 2024 Shop Thời Trang. All rights reserved.</p>
        </div>
    </div>
</footer>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Initialize Hero Slider
        const heroSwiper = new Swiper('.hero-slider', {
            loop: true,
            autoplay: {
                delay: 5000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            }
        });

        // Initialize Products Slider
        const productsSwiper = new Swiper('.products-slider', {
            slidesPerView: 1,
            spaceBetween: 20,
            loop: true,
            autoplay: {
                delay: 3000,
                disableOnInteraction: false,
            },
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            breakpoints: {
                576: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                992: {
                    slidesPerView: 4,
                }
            }
        });

        // Add to cart with animation
        $('.add-to-cart-form').on('submit', function(e) {
            e.preventDefault();
            const form = $(this);
            const button = form.find('.add-to-cart-btn');
            const originalText = button.html();

            // Show loading
            button.html('<i class="fas fa-spinner fa-spin me-2"></i>Đang thêm...');
            button.prop('disabled', true);

            // Submit form
            $.ajax({
                url: form.attr('action'),
                method: 'POST',
                data: form.serialize(),
                success: function(response) {
                    // Success animation
                    button.html('<i class="fas fa-check me-2"></i>Đã thêm!');
                    button.removeClass('btn-primary').addClass('btn-success');

                    // Update cart count
                    updateCounts();

                    // Reset button after 2 seconds
                    setTimeout(function() {
                        button.html(originalText);
                        button.removeClass('btn-success').addClass('btn-primary');
                        button.prop('disabled', false);
                    }, 2000);

                    // Show success toast
                    showToast('Đã thêm sản phẩm vào giỏ hàng!', 'success');
                },
                error: function() {
                    // Error handling
                    button.html(originalText);
                    button.prop('disabled', false);
                    showToast('Có lỗi xảy ra, vui lòng thử lại!', 'error');
                }
            });
        });

        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $($(this).attr('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 800);
            }
        });
    });

    // Toast notification function
    function showToast(message, type = 'success') {
        const toast = $(`
            <div class="toast-notification ${type}">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                ${message}
            </div>
        `);

        $('body').append(toast);

        setTimeout(() => {
            toast.addClass('show');
        }, 100);

        setTimeout(() => {
            toast.removeClass('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }
</script>

<style>
    .toast-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        border-left: 4px solid;
    }

    .toast-notification.success {
        border-left-color: #10b981;
        color: #10b981;
    }

    .toast-notification.error {
        border-left-color: #ef4444;
        color: #ef4444;
    }

    .toast-notification.show {
        transform: translateX(0);
    }
</style>
@endsection
