<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description', 'Shop thời trang hiện đại - B<PERSON> sưu tập áo quần thời trang nam nữ chất lượng cao, gi<PERSON> tốt nhất. Giao hàng toàn quốc, đổi trả miễn phí.'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords', 'shop thời trang, áo quần, thời trang nam, thời trang nữ, quần áo đẹp, giá rẻ'); ?>">
    <meta name="author" content="Shop Thời Trang">
    <meta property="og:title" content="<?php echo $__env->yieldContent('title', 'Shop Thời Trang - <PERSON><PERSON> sưu tập thời trang hiện đại'); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('meta_description', 'Shop thời trang hiện đại với bộ sưu tập đa dạng, chất lượng cao'); ?>">
    <meta property="og:image" content="<?php echo e(asset('img/logo.png')); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta name="twitter:card" content="summary_large_image">
    
    <title><?php echo $__env->yieldContent('title', 'Shop Thời Trang - Bộ sưu tập thời trang hiện đại'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="<?php echo e(asset('img/logo.png')); ?>">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <?php echo $__env->yieldContent('styles'); ?>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f59e0b;
            --accent-color: #10b981;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --text-color: #374151;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: #ffffff;
        }
        
        .font-display {
            font-family: 'Playfair Display', serif;
        }
        
        /* Header Styles */
        .modern-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .top-bar {
            background: rgba(0, 0, 0, 0.1);
            padding: 8px 0;
            font-size: 14px;
        }
        
        .main-header {
            padding: 15px 0;
        }
        
        .logo img {
            height: 60px;
            width: auto;
            object-fit: contain;
        }
        
        .search-form {
            max-width: 500px;
            margin: 0 auto;
        }
        
        .search-input {
            border: none;
            border-radius: 50px;
            padding: 12px 20px;
            font-size: 16px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .search-btn {
            border-radius: 50px;
            padding: 12px 25px;
            background: var(--secondary-color);
            border: none;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: #d97706;
            transform: translateY(-2px);
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .header-link {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .header-link:hover {
            color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }
        
        .header-link i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .header-link span {
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            min-width: 18px;
            text-align: center;
        }
        
        /* Navigation */
        .main-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid var(--border-color);
            padding: 10px 0;
        }
        
        .nav-link {
            color: var(--text-color);
            font-weight: 500;
            padding: 12px 16px;
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 0 2px;
            white-space: nowrap;
            font-size: 14px;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background: var(--light-color);
            transform: translateY(-2px);
        }
        
        .nav-link i {
            font-size: 12px;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: var(--shadow-lg);
            border-radius: 12px;
            padding: 10px 0;
        }
        
        .dropdown-item {
            padding: 8px 20px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .dropdown-item:hover {
            background: var(--light-color);
            color: var(--primary-color);
        }
        
        /* Alerts */
        .alert {
            border: none;
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }
        
        .alert-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.95);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .header-actions {
                gap: 8px;
            }
            
            .header-link span {
                display: none;
            }
            
            .header-link {
                padding: 8px;
            }
            
            .search-form {
                margin: 10px 0;
            }
            
            .main-nav .nav {
                flex-wrap: wrap;
                justify-content: flex-start !important;
            }
            
            .nav-link {
                padding: 8px 12px;
                font-size: 13px;
                margin: 2px 1px;
            }
            
            .nav-link i {
                display: none;
            }
        }
        
        @media (max-width: 576px) {
            .nav-link {
                padding: 6px 8px;
                font-size: 12px;
            }
            
            .main-nav {
                padding: 5px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Header -->
    <header class="modern-header">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex gap-3 text-white-50">
                            <span><i class="fas fa-phone me-1"></i> +84 123 456 789</span>
                            <span><i class="fas fa-envelope me-1"></i> <EMAIL></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex gap-2 justify-content-end">
                            <a href="#" class="text-white-50"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="text-white-50"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="text-white-50"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <!-- Logo -->
                    <div class="col-lg-2 col-md-3 col-6">
                        <a href="<?php echo e(route('home.index')); ?>" class="logo">
                            <img src="<?php echo e(asset('img/logo.png')); ?>" alt="Shop Thời Trang" class="img-fluid">
                        </a>
                    </div>

                    <!-- Search -->
                    <div class="col-lg-6 col-md-5 col-12 order-md-2 order-3">
                        <form action="<?php echo e(route('user.searchProduct')); ?>" method="GET" class="search-form">
                            <div class="input-group">
                                <input type="text" name="keyword" class="form-control search-input" 
                                       placeholder="Tìm kiếm sản phẩm..." required>
                                <button class="btn search-btn" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Actions -->
                    <div class="col-lg-4 col-md-4 col-6 order-md-3 order-2">
                        <div class="header-actions justify-content-end">
                            <?php if(Session::get('id_user')): ?>
                                <a href="<?php echo e(route('cart.indexCart')); ?>" class="header-link no-loading">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Giỏ hàng</span>
                                    <span class="badge-count" id="cartCount"><?php echo e($cartCount ?? 0); ?></span>
                                </a>
                                <a href="<?php echo e(route('order.orderIndex')); ?>" class="header-link no-loading">
                                    <i class="fas fa-file-invoice"></i>
                                    <span>Đơn hàng</span>
                                    <span class="badge-count" id="orderCount"><?php echo e($orderCount ?? 0); ?></span>
                                </a>
                                <div class="dropdown">
                                    <a href="#" class="header-link dropdown-toggle no-loading" data-bs-toggle="dropdown">
                                        <i class="fas fa-user"></i>
                                        <span>Tài khoản</span>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Thông tin</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo e(route('signout')); ?>"><i class="fas fa-sign-out-alt me-2"></i>Đăng xuất</a></li>
                                    </ul>
                                </div>
                            <?php else: ?>
                                <a href="<?php echo e(route('login')); ?>" class="header-link">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>Đăng nhập</span>
                                </a>
                                <a href="/register" class="header-link">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Đăng ký</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Menu -->
    <nav class="main-nav">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <ul class="nav justify-content-center">
                        <li class="nav-item">
                            <a class="nav-link no-loading" href="<?php echo e(route('home.index')); ?>">
                                <i class="fas fa-home me-1"></i>Trang chủ
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle no-loading" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-th-large me-1"></i>Danh mục
                            </a>
                            <ul class="dropdown-menu">
                                <?php if(isset($categorys) && $categorys->count() > 0): ?>
                                    <?php $__currentLoopData = $categorys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('user.filterProduct', ['category' => $category->id_category])); ?>"><?php echo e($category->name_category); ?></a></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('user.filterProduct')); ?>">Tất cả sản phẩm</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle no-loading" href="#" data-bs-toggle="dropdown">
                                <i class="fas fa-industry me-1"></i>Nhà sản xuất
                            </a>
                            <ul class="dropdown-menu">
                                <?php if(isset($manufacturers) && $manufacturers->count() > 0): ?>
                                    <?php $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item" href="<?php echo e(route('user.filterProduct', ['manufacturer' => $manufacturer->id_manufacturer])); ?>"><?php echo e($manufacturer->name_manufacturer); ?></a></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('user.filterProduct')); ?>">Tất cả nhà sản xuất</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('post.indexListPostUser')); ?>">
                                <i class="fas fa-newspaper me-1"></i>Bài viết
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link no-loading" href="#contact">
                                <i class="fas fa-phone me-1"></i>Liên hệ
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Alerts -->
    <div class="container mt-3">
        <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-down">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" data-aos="fade-down">
                <ul class="mb-0">
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li><i class="fas fa-exclamation-triangle me-2"></i><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Main Content -->
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });

        // Loading functions
        function showLoading() {
            $('#loadingOverlay').fadeIn(300);
        }

        function hideLoading() {
            $('#loadingOverlay').fadeOut(300);
        }

        // Update cart and order counts
        function updateCounts() {
            // Only update if user is logged in (check if cart/order elements exist)
            if ($('#cartCount').length && $('#orderCount').length) {
                try {
                    $.get('/cart/count', function(data) {
                        $('#cartCount').text(data.count || 0);
                    }).fail(function() {
                        $('#cartCount').text('0');
                    });
                } catch(e) {
                    $('#cartCount').text('0');
                }

                try {
                    $.get('/order/count', function(data) {
                        $('#orderCount').text(data.count || 0);
                    }).fail(function() {
                        $('#orderCount').text('0');
                    });
                } catch(e) {
                    $('#orderCount').text('0');
                }
            }
        }

        $(document).ready(function() {
            // Hide loading on page load
            hideLoading();

            // Update counts
            updateCounts();

            // Show loading on form submit (except AJAX forms)
            $('form:not(.ajax-form):not(.no-loading)').on('submit', function() {
                showLoading();
            });

            // Show loading on navigation links (except special links)
            $('a:not([href^="#"]):not([target="_blank"]):not(.dropdown-toggle):not(.pagination a):not(.no-loading)').on('click', function(e) {
                const href = $(this).attr('href');
                if (href && href !== 'javascript:void(0)' && href !== '#') {
                    showLoading();
                }
            });

            // Handle pagination with AJAX (prevent page reload)
            $(document).on('click', '.pagination a', function(e) {
                e.preventDefault();
                const url = $(this).attr('href');

                if (url) {
                    showLoading();

                    $.get(url)
                        .done(function(data) {
                            const newContent = $(data).find('.all-products-section').html();
                            if (newContent) {
                                $('.all-products-section').html(newContent);

                                $('html, body').animate({
                                    scrollTop: $('.all-products-section').offset().top - 100
                                }, 500);
                            }
                        })
                        .fail(function() {
                            window.location.href = url;
                        })
                        .always(function() {
                            hideLoading();
                        });
                }
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);

            // Hide loading when browser back/forward buttons are used
            $(window).on('pageshow', function(event) {
                if (event.originalEvent.persisted) {
                    hideLoading();
                }
            });

            // Hide loading on window focus
            $(window).on('focus', function() {
                setTimeout(hideLoading, 100);
            });
        });
    </script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/layouts/app.blade.php ENDPATH**/ ?>