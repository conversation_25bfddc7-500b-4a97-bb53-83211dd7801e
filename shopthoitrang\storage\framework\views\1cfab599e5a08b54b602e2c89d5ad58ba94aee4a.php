
<?php $__env->startSection('title', 'Tìm kiếm đơn hàng'); ?>
<?php $__env->startSection('page-title', 'Tìm kiếm đơn hàng'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Card chính -->
    <div class="card card-info card-outline">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">
                    <i class="fas fa-search mr-2"></i>Kết quả tìm kiếm đơn hàng
                </h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('admin.orderindexAdmin')); ?>" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left mr-1"></i>Quay lại danh sách
                    </a>
                </div>
            </div>
        </div>

        <!-- <PERSON><PERSON> tìm kiếm -->
        <div class="card-body pb-0">
            <form action="<?php echo e(route('admin.adminSearchOrder')); ?>" method="GET" class="mb-3">
                <div class="input-group">
                    <input type="text" name="id" class="form-control"
                           placeholder="Tìm kiếm theo mã đơn hàng hoặc mã người dùng..."
                           value="<?php echo e(request('id')); ?>">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                        <a href="<?php echo e(route('admin.orderindexAdmin')); ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Xóa
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 100px;" class="text-center">Mã đơn hàng</th>
                        <th style="width: 100px;" class="text-center">Mã khách hàng</th>
                        <th style="width: 150px;" class="text-right">Tổng tiền</th>
                        <th>Địa chỉ giao hàng</th>
                        <th style="width: 150px;" class="text-center">Ngày đặt</th>
                        <th style="width: 140px;" class="text-center">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if($orders && count($orders) > 0): ?>
                        <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td class="text-center">
                                <span class="badge badge-primary">#<?php echo e($order->id_order); ?></span>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-secondary"><?php echo e($order->id_user); ?></span>
                            </td>
                            <td class="text-right font-weight-bold text-success">
                                <?php echo e(number_format($order->total_order, 0, ',', '.')); ?>đ
                            </td>
                            <td>
                                <div class="address-info">
                                    <i class="fas fa-map-marker-alt text-muted mr-1"></i>
                                    <?php echo e(Str::limit($order->address, 50)); ?>

                                    <?php if(strlen($order->address) > 50): ?>
                                        <button class="btn btn-link btn-sm p-0 ml-1"
                                                onclick="showFullAddress('<?php echo e(addslashes($order->address)); ?>')">
                                            <i class="fas fa-expand-alt"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="text-center text-muted">
                                <div><?php echo e($order->created_at->format('d/m/Y')); ?></div>
                                <small><?php echo e($order->created_at->format('H:i')); ?></small>
                            </td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <a href="<?php echo e(route('admin.adminDetailsOrderIndex', ['id_order' => $order->id_order])); ?>"
                                       class="btn btn-sm btn-info"
                                       title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button"
                                            class="btn btn-sm btn-danger btn-cancel-order"
                                            data-order-id="<?php echo e($order->id_order); ?>"
                                            data-order-total="<?php echo e(number_format($order->total_order, 0, ',', '.')); ?>"
                                            title="Hủy đơn hàng">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-search fa-3x mb-3"></i>
                                    <p class="mb-0">Không tìm thấy đơn hàng nào</p>
                                    <?php if(request('id')): ?>
                                        <small>Không có kết quả cho "<?php echo e(request('id')); ?>"</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                    </tbody>
                </table>
                <div class="clearfix">
                    <div class="hint-text">Showing <b>5</b> out of <b>25</b> entries</div>
                    <ul class="pagination">
                        <li class="page-item"><a href="#" class="page-link">Previous</a></li>
                        <li class="page-item"><a href="#" class="page-link">1</a></li>
                        <li class="page-item"><a href="#" class="page-link">2</a></li>
                        <li class="page-item active"><a href="#" class="page-link">3</a></li>
                        <li class="page-item"><a href="#" class="page-link">4</a></li>
                        <li class="page-item"><a href="#" class="page-link">5</a></li>
                        <li class="page-item"><a href="#" class="page-link">Next</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>
<style>
    .input-search{
        width: 30%;
        height: 40px;
        border-radius: 15px;
        padding-left: 15px;
    }
    .btn-warning{
        color: white;
        font-weight: bold;
    }
    .btn-warning:hover{
        color: white;
    }
</style>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/order/findorder.blade.php ENDPATH**/ ?>