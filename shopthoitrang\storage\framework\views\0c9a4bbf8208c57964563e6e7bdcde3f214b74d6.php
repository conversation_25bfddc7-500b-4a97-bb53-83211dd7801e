
<?php $__env->startSection('title', 'Quản lý sản phẩm'); ?>
<?php $__env->startSection('page-title', 'Quản lý sản phẩm'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Card chính -->
    <div class="card card-primary card-outline">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h3 class="card-title">
                    <i class="fas fa-shopping-bag mr-2"></i>Danh sách sản phẩm
                </h3>
                <div class="card-tools">
                    <a href="<?php echo e(route('product.addproduct')); ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-plus mr-1"></i>Thêm sản phẩm
                    </a>
                </div>
            </div>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
                <thead class="thead-light">
                    <tr>
                        <th style="width: 80px;" class="text-center">Mã SP</th>
                        <th>Tên sản phẩm</th>
                        <th>Danh mục</th>
                        <th>Hãng SX</th>
                        <th style="width: 100px;" class="text-center">Số lượng</th>
                        <th style="width: 120px;" class="text-right">Giá</th>
                        <th style="width: 100px;" class="text-center">Ảnh</th>
                        <th style="width: 100px;">Size</th>
                        <th style="width: 100px;">Màu sắc</th>
                        <th style="width: 140px;" class="text-center">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td class="text-center">
                            <span class="badge badge-primary"><?php echo e($product->id_product); ?></span>
                        </td>
                        <td class="font-weight-medium"><?php echo e($product->name_product); ?></td>
                        <td>
                            <?php $__currentLoopData = $categorys; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($product->id_category == $category->id_category): ?>
                                    <span class="badge badge-info"><?php echo e($category->name_category); ?></span>
                                    <?php break; ?>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </td>
                        <td>
                            <?php $__currentLoopData = $manufacturers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $manufacturer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($product->id_manufacturer == $manufacturer->id_manufacturer): ?>
                                    <span class="badge badge-secondary"><?php echo e($manufacturer->name_manufacturer); ?></span>
                                    <?php break; ?>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-primary badge-pill"><?php echo e($product->quantity_product); ?></span>
                        </td>
                        <td class="text-right font-weight-bold text-success">
                            <?php echo e(number_format($product->price_product, 0, ',', '.')); ?>đ
                        </td>
                        <td class="text-center">
                            <img src="<?php echo e(asset('uploads/productimage/' . $product->image_address_product)); ?>"
                                 alt="<?php echo e($product->name_product); ?>"
                                 class="img-thumbnail product-img"
                                 style="width: 60px; height: 60px; object-fit: cover;">
                        </td>
                        <td>
                            <?php if($product->sizes): ?>
                                <?php $__currentLoopData = explode(',', $product->sizes); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge badge-dark mr-1"><?php echo e(trim($size)); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($product->colors): ?>
                                <?php $__currentLoopData = explode(',', $product->colors); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <span class="badge badge-light border mr-1"><?php echo e(trim($color)); ?></span>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td class="text-center">
                            <div class="btn-group">
                                <a href="<?php echo e(route('product.indexUpdateproduct', ['id' => $product->id_product])); ?>"
                                   class="btn btn-sm btn-warning"
                                   title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo e(route('product.deleteproduct', ['id' => $product->id_product])); ?>"
                                   class="btn btn-sm btn-danger btn-delete"
                                   title="Xóa">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <p class="mb-0">Không có sản phẩm nào</p>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Phân trang -->
        <?php if($products->hasPages()): ?>
        <div class="card-footer clearfix">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    Hiển thị <?php echo e($products->firstItem() ?? 0); ?> đến <?php echo e($products->lastItem() ?? 0); ?>

                    trong tổng số <?php echo e($products->total()); ?> sản phẩm
                </div>
                <div>
                    <?php echo e($products->links()); ?>

                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .product-img {
        transition: transform 0.3s ease;
        cursor: pointer;
    }

    .product-img:hover {
        transform: scale(2);
        z-index: 1000;
        position: relative;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }

    .badge {
        font-size: 0.75em;
    }

    .table td {
        vertical-align: middle;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    $(document).ready(function() {
        // Hiệu ứng chuyển trang mượt mà
        $(document).on('click', '.pagination a', function(event) {
            event.preventDefault();
            var url = $(this).attr('href');
            showLoading();
            setTimeout(function() {
                window.location.href = url;
            }, 300);
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Downloads\doannhomD\shopthoitrang\resources\views/admin/product/listproduct.blade.php ENDPATH**/ ?>