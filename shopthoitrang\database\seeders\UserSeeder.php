<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('users')->insert([
            'name' => 'luong',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'phone' => '0123456789',
            'address' => '<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>h<PERSON>',
            'role' => 0 // hoặc 1, tuỳ <PERSON> bạn
        ]);

        DB::table('users')->insert([
            'name' => 'thanh',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'phone' => '0123456789',
            'address' => '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>',
            'role' => 0 // hoặc 1, tu<PERSON> bạn
        ]);
        
        DB::table('users')->insert([
            'name' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin'),
            'phone' => '1278912321',
            'address' => 'Thành phố Hồ Chí Minh',
            'role' => 1 // hoặc 1, tuỳ ý bạn
        ]);
    }
}
