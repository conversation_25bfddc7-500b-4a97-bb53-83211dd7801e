@extends('admin.layout')
@section('title', 'Quản lý danh mục')
@section('page-title', 'Quản lý danh mục')
@section('meta_description', 'Trang quản lý danh mục sản phẩm, th<PERSON><PERSON>, s<PERSON><PERSON>, x<PERSON><PERSON> danh mục cho hệ thống bán hàng thời trang.')
@section('meta_keywords', 'danh mục, quản lý danh mục, admin, sản phẩm, thời trang')
@section('content')
<div class="container-fluid">
    <div class="row mb-2">
        <div class="col-12">
            <a href="{{ route('category.create') }}" class="btn btn-primary mb-3">
                <i class="fas fa-plus"></i> Thêm danh mục
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-header">
            <h1 class="card-title h5"><PERSON><PERSON> s<PERSON>ch danh mục</h1>
            <div class="card-tools">
                <form action="{{ route('category.index') }}" method="GET" class="input-group input-group-sm" style="width: 250px;">
                    <input type="text" name="search" class="form-control float-right" placeholder="Tìm kiếm..." value="{{ request('search') }}" aria-label="Tìm kiếm danh mục">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-default" aria-label="Tìm kiếm"><i class="fas fa-search"></i></button>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body p-0">
            <table class="table table-striped table-hover" aria-describedby="Danh sách danh mục">
                <thead>
                    <tr>
                        <th style="width: 10px">#</th>
                        <th>Tên danh mục</th>
                        <th>Ngày tạo</th>
                        <th>Cập nhật</th>
                        <th style="width: 120px">Hành động</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($categories as $key => $category)
                        <tr>
                            <td>{{ ($categories->currentPage() - 1) * $categories->perPage() + $key + 1 }}</td>
                            <td>{{ $category->name_category }}</td>
                            <td>{{ $category->created_at ? $category->created_at->format('d/m/Y') : '-' }}</td>
                            <td>{{ $category->updated_at ? $category->updated_at->format('d/m/Y') : '-' }}</td>
                            <td>
                                <a href="{{ route('category.edit', $category->id_category) }}" class="btn btn-sm btn-warning" title="Sửa"><i class="fas fa-edit"></i></a>
                                <form action="{{ route('category.destroy', $category->id_category) }}" method="POST" style="display:inline-block">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc muốn xóa?')" title="Xóa"><i class="fas fa-trash"></i></button>
                                </form>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="text-center">Không có danh mục nào</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="card-footer clearfix">
            {{ $categories->links('pagination::bootstrap-4') }}
        </div>
    </div>
</div>
@endsection
@section('scripts')
<script>
    // Hiệu ứng chuyển trang mượt mà
    $(document).on('click', '.pagination a', function(event) {
        event.preventDefault();
        var url = $(this).attr('href');
        $('body').fadeOut(150, function() {
            window.location.href = url;
        });
    });
</script>
@endsection