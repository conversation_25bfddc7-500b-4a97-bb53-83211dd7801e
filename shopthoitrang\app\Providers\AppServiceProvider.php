<?php

namespace App\Providers;
use Illuminate\Support\Facades\View;
use App\Models\Manufacturer;
use App\Http\View\Composers\GlobalDataComposer;

use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Paginator::useBootstrap();

        // Đăng ký View Composer cho dashboard_user
        View::composer('user.dashboard_user', function ($view) {
            $manufacturers = Manufacturer::all();
            $view->with('manufacturers', $manufacturers);
        });

        // Đăng ký View Composer cho layout modern
        View::composer('layouts.modern', GlobalDataComposer::class);
    }
}
